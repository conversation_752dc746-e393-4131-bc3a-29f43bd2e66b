#!/usr/bin/env python3
"""
测试改进后的CSV分割策略
"""

import pandas as pd
import numpy as np
from pathlib import Path
from src.csv_risk_processor import CSVRiskProcessor


def create_test_data():
    """创建不同规模的测试数据"""
    
    # 测试数据1：模拟31KB文件 - 352行，每行较长
    print("创建测试数据1：模拟31KB文件（352行，每行较长）")
    long_data = {
        'user_id': [f'user_{i:06d}' for i in range(352)],
        'feature_long_name_1': [f'very_long_feature_value_content_{i:010d}' for i in range(352)],
        'feature_long_name_2': [f'another_long_feature_with_text_content_{i:010d}' for i in range(352)],
        'feature_long_name_3': [f'third_feature_with_descriptive_text_{i:010d}' for i in range(352)],
        'risk_score': np.random.uniform(0, 1, 352),
        'credit_score': np.random.randint(300, 850, 352),
    }
    df_long = pd.DataFrame(long_data)
    df_long.to_csv('test_31kb_like.csv', index=False)
    
    # 测试数据2：模拟118KB文件 - 1200行，每行较短
    print("创建测试数据2：模拟118KB文件（1200行，每行较短）")
    short_data = {
        'id': list(range(1200)),
        'score': np.random.uniform(0, 100, 1200),
        'flag': ['A' if i % 2 == 0 else 'B' for i in range(1200)],
        'category': np.random.choice(['X', 'Y', 'Z'], 1200)
    }
    df_short = pd.DataFrame(short_data)
    df_short.to_csv('test_118kb_like.csv', index=False)
    
    return df_long, df_short


def test_chunking_strategy():
    """测试分割策略"""
    
    # 创建测试数据
    df_long, df_short = create_test_data()
    
    # 创建处理器
    processor = CSVRiskProcessor()
    
    print("\n" + "="*60)
    print("测试改进后的分割策略")
    print("="*60)
    
    # 测试长行数据
    print(f"\n📊 测试长行数据：{len(df_long)} 行，{len(df_long.columns)} 列")
    file_code_long, docs_long, info_long = processor.process_csv_file('test_31kb_like.csv')
    
    print(f"✅ 分割结果：{len(docs_long)} 个文档块")
    print(f"平均每块行数：{len(df_long) / len(docs_long):.1f} 行")
    
    # 分析每个块的详细信息
    print("\n前5个块的详细信息：")
    for i, doc in enumerate(docs_long[:5]):
        meta = doc.metadata
        print(f"  块{i+1}: {meta['rows_in_chunk']}行, {meta['chunk_size_chars']}字符, "
              f"范围:{meta['chunk_start_row']}-{meta['chunk_end_row']}")
    
    # 测试短行数据
    print(f"\n📊 测试短行数据：{len(df_short)} 行，{len(df_short.columns)} 列")
    file_code_short, docs_short, info_short = processor.process_csv_file('test_118kb_like.csv')
    
    print(f"✅ 分割结果：{len(docs_short)} 个文档块")
    print(f"平均每块行数：{len(df_short) / len(docs_short):.1f} 行")
    
    # 分析每个块的详细信息
    print("\n前5个块的详细信息：")
    for i, doc in enumerate(docs_short[:5]):
        meta = doc.metadata
        print(f"  块{i+1}: {meta['rows_in_chunk']}行, {meta['chunk_size_chars']}字符, "
              f"范围:{meta['chunk_start_row']}-{meta['chunk_end_row']}")
    
    # 对比分析
    print(f"\n" + "="*60)
    print("对比分析")
    print("="*60)
    print(f"长行数据 (类似31KB)：{len(df_long)}行 → {len(docs_long)}块，块/行比例 = {len(docs_long)/len(df_long):.3f}")
    print(f"短行数据 (类似118KB)：{len(df_short)}行 → {len(docs_short)}块，块/行比例 = {len(docs_short)/len(df_short):.3f}")
    
    # 检查内容覆盖率
    print(f"\n📋 内容覆盖验证：")
    
    # 验证长行数据覆盖
    covered_rows_long = set()
    for doc in docs_long:
        meta = doc.metadata
        for row in range(meta['chunk_start_row'], meta['chunk_end_row'] + 1):
            covered_rows_long.add(row)
    coverage_long = len(covered_rows_long) / len(df_long)
    print(f"长行数据覆盖率：{coverage_long:.2%} ({len(covered_rows_long)}/{len(df_long)} 行)")
    
    # 验证短行数据覆盖
    covered_rows_short = set()
    for doc in docs_short:
        meta = doc.metadata
        for row in range(meta['chunk_start_row'], meta['chunk_end_row'] + 1):
            covered_rows_short.add(row)
    coverage_short = len(covered_rows_short) / len(df_short)
    print(f"短行数据覆盖率：{coverage_short:.2%} ({len(covered_rows_short)}/{len(df_short)} 行)")
    
    # 显示示例文档内容
    print(f"\n" + "="*60)
    print("示例文档内容（第一个块）")
    print("="*60)
    print(docs_long[0].page_content[:500] + "..." if len(docs_long[0].page_content) > 500 else docs_long[0].page_content)


def analyze_original_vs_improved():
    """对比原来的方法和改进后的方法"""
    
    print("\n" + "="*60)
    print("原方法 vs 改进方法对比")
    print("="*60)
    
    # 读取测试数据
    df_long = pd.read_csv('test_31kb_like.csv')
    
    processor = CSVRiskProcessor()
    
    # 原方法模拟（基于字符的分割）
    print("🔍 模拟原方法（基于字符分割）的行为...")
    full_text = processor._convert_df_to_markdown(df_long, "test")
    
    from langchain_text_splitters import RecursiveCharacterTextSplitter
    text_splitter = RecursiveCharacterTextSplitter.from_language(
        language="markdown",
        chunk_size=1024,
        chunk_overlap=100,
    )
    old_chunks = text_splitter.split_text(full_text)
    
    print(f"原方法结果：{len(old_chunks)} 个块")
    
    # 新方法
    print("🚀 使用改进方法...")
    _, new_docs, _ = processor.process_csv_file('test_31kb_like.csv')
    
    print(f"改进方法结果：{len(new_docs)} 个块")
    print(f"改进程度：块数量减少 {((len(old_chunks) - len(new_docs)) / len(old_chunks) * 100):.1f}%")


if __name__ == "__main__":
    test_chunking_strategy()
    analyze_original_vs_improved()
    
    # 清理测试文件
    import os
    for file in ['test_31kb_like.csv', 'test_118kb_like.csv']:
        if os.path.exists(file):
            os.remove(file)
    print("\n✅ 测试完成，临时文件已清理") 