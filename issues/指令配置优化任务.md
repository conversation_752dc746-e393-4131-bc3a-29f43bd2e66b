# 指令配置优化任务

## 任务背景

用户希望将当前硬编码在 `risk_config.py` 中的 LLM 指令迁移到 Markdown 文件中，以便更方便地调整和管理指令内容。

## 实施方案

采用多文件分类管理方案，支持指令模板的灵活配置和扩展。

## 实施计划

### 1. 创建 prompts 目录结构 ✅
- 创建 `prompts/` 目录
- 创建 `prompts/risk_analysis/` 子目录
- 创建 `prompts/risk_analysis/default.md` 默认指令模板

### 2. 迁移现有指令到 Markdown 文件 ✅
- 将 `RISK_ANALYSIS_PROMPT` 内容迁移到 `default.md`
- 保持原有格式和占位符

### 3. 创建使用说明文档 ✅
- 创建 `prompts/README.md` 说明文档
- 提供指令模板编写指南

### 4. 添加配置支持 ✅
- 在 `config.py` 中添加 `RISK_ANALYSIS_PROMPT_FILE` 配置项
- 支持环境变量配置

### 5. 扩展 RiskAnalysisConfig 类 ✅
- 添加 `load_prompt_from_file()` 方法
- 修改 `get_risk_analysis_prompt()` 方法
- 添加缓存机制
- 保证向后兼容性

### 6. 测试验证 🔄
- 测试从 Markdown 文件读取指令
- 验证占位符替换功能
- 确保向后兼容性

### 7. 文档更新
- 更新相关文档说明新的配置方式

## 技术实现要点

### 文件读取逻辑
1. 优先从指定的文件路径读取
2. 支持环境变量 `RISK_ANALYSIS_PROMPT_FILE` 配置
3. 回退到配置文件中的默认路径
4. 最终回退到硬编码的备用指令

### 缓存机制
- 缓存已读取的指令内容
- 避免重复文件读取操作
- 支持文件路径变更时重新加载

### 向后兼容性
- 保留原有的硬编码指令作为备用
- 现有代码无需修改即可正常工作
- 文件不存在时自动回退到备用方案

## 使用方式

### 1. 编辑指令模板
```bash
vim prompts/risk_analysis/default.md
```

### 2. 配置自定义指令文件
```python
# config.py
RISK_ANALYSIS_PROMPT_FILE = "prompts/risk_analysis/custom.md"
```

### 3. 环境变量配置
```bash
export RISK_ANALYSIS_PROMPT_FILE="prompts/risk_analysis/custom.md"
```

## 预期效果

1. **灵活性提升**：用户可以通过编辑 Markdown 文件轻松调整指令
2. **版本管理**：指令模板可以进行版本控制
3. **扩展性增强**：支持多种指令模板和业务场景
4. **维护性改善**：指令与代码分离，便于维护
5. **兼容性保证**：现有系统无缝升级

## 风险控制

1. **文件读取异常**：提供完整的异常处理和日志记录
2. **向后兼容**：保留硬编码备用方案
3. **缓存机制**：避免频繁文件读取影响性能
4. **路径安全**：使用相对路径，避免安全风险
